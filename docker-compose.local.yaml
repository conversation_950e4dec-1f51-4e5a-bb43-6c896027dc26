services:

  app:
    build:
      context: .
      target: dev
      dockerfile: ./Dockerfile
      args:
        INSTALL_DEV: 'true'
    container_name: rpm_registration_api
    restart: unless-stopped
    tty: true
    env_file: .env
    ports:
      - "8001:8000"
    entrypoint: /app/entrypoint.sh
    volumes:
      - .:/app/

  db:
    image: postgres:14
    container_name: rpm_registration_api_db
    restart: unless-stopped
    tty: true
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=rpm_single
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - rpm_registration_db:/var/lib/postgresql

  localstack:
    container_name: localstack_rpm
    build:
      context: .
      dockerfile: Dockerfile.localstack
    ports:
      - "4566:4566"  # Edge port
      - "4571:4571"  # Old edge port
    environment:
      - SERVICES=sqs
      - DEBUG=1  # Optional: to enable debug mode
      - HOSTNAME_EXTERNAL=localstack-sqs
    volumes:
      - localstack-data:/var/lib/localstack
      - ./local-aws-init.sh:/etc/localstack/init/ready.d/local-aws-init.sh
    networks:
      - localstack-net


volumes:
  rpm_registration_db:
    driver: local
  localstack-data:
    driver: local

networks:
  localstack-net:
    name: localstack-net
