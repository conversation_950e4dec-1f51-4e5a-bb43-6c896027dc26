from typing import Dict, Any

import pendulum
from fastapi import (
    HTTPException,
)

from app.log.logging import logger
from app.routers.requests.api_request import PairDeviceRequest

from ciba_iot_etl.extract.transtek_api.core import MioConnectClient
from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.models.pydantic.devices import MemberDeviceData
from ciba_iot_etl.models.pydantic.transtek import MODEL_NUMBER_TO_TYPE
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.repositories.devices_repository import (
    MemberDevicesRepository,
)

from ciba_iot_etl.extract.transtek_api.common import MioConnectError


async def get_member_by_request(request_data: PairDeviceRequest) -> Member:
    member = await Member.get_by_platform(
        platform_type=request_data.member_type.value,
        platform_id=request_data.member_id,
    )
    if not member:
        logger.warning(
            f"Member not found: platform_type={request_data.member_type.value}, "
            f"platform_id={request_data.member_id}"
        )
        raise HTTPException(status_code=404, detail="Member not found")

    logger.info(f"Found member: {member.id}")
    return member


async def get_device_info(
    request_data: PairDeviceRequest, client: MioConnectClient
) -> Dict[str, Any]:
    try:
        if request_data.imei:
            logger.info(f"Retrieving device by IMEI: {request_data.imei}")
            device = await client.get_device_by_imei(request_data.imei)
        elif request_data.serial_number:
            logger.info(
                f"Retrieving device by serial number: {request_data.serial_number}"
            )
            device = await client.get_device(request_data.serial_number)
        else:
            raise HTTPException(
                status_code=400,
                detail="Either IMEI or serial number must be provided",
            )

        if not device or "serialNumber" not in device:
            raise HTTPException(
                status_code=404,
                detail="Device not found or invalid device data",
            )

        logger.info(f"Retrieved device: {device.get('serialNumber')}")
        return device

    except MioConnectError as e:
        logger.error(f"Failed to retrieve device: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Failed to retrieve device information: {str(e)}",
        )


async def activate_device(device_id: str, client: MioConnectClient) -> None:
    try:
        logger.info(f"Activating device: {device_id}")
        await client.activate_device(device_id=device_id)
        logger.info(f"Device activated successfully: {device_id}")
    except MioConnectError as e:
        logger.error(f"Device activation failed for {device_id}: {e}")
        raise HTTPException(
            status_code=400, detail=f"Device activation failed: {str(e)}"
        )


async def save_device_pairing(member: Member, device: Dict[str, Any]) -> None:
    try:
        device_model = device.get("modelNumber", "Unknown")
        device_type = (
            MODEL_NUMBER_TO_TYPE[device_model]
            if "modelNumber" in device
            else "Unknown"
        )
        device_data = MemberDeviceData(
            last_synced_at=pendulum.now(),
            external_id=device["serialNumber"],
            device_type=device_type,
            vendor=DeviceType.TRANSTEK,
        )
        await MemberDevicesRepository.add_device(
            member_id=member.id, device_data=device_data
        )

        logger.info(
            f"Device pairing saved: member_id={member.id}, "
            f"device_id={device['serialNumber']}"
        )

    except Exception as e:
        logger.error(f"Failed to save device pairing: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to save device pairing to database"
        )
