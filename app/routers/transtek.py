from json import dumps
from typing import Optional

import pendulum
from fastapi import (
    APIRouter,
    Request,
    Response,
    status,
    HTTPException,
    Depends,
    Query,
)

from app.pydantic_model.transtek import (
    CarrierListResponse,
    UpdateTrackingDataResponse,
    TranstekResponse,
)

from ciba_iot_etl.models.pydantic.transtek import (
    TranstekStatusMessage,
    TranstekStatusTestMessage,
    TranstekTelemetryMessage,
    TranstekTelemetryTestMessage,
)
from ciba_iot_etl.repositories.transtek_repository import TranstekRepository


from app.routers.requests.api_request import UpdateTrackingDataRequest
from ciba_iot_etl.models.pydantic.common import TRACKING_URLS
from app.helpers.transtek import (
    activate_device,
    get_device_info,
    get_member_by_request,
    save_device_pairing,
)
from app.log.logging import logger
from app.routers.requests.api_request import PairDeviceRequest
from app.services.queue import SendQueueException, get_queue
from app.settings import get_settings

from ciba_iot_etl.extract.transtek_api.core import MioConnectClient
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.transtek import Transtek
from ciba_iot_etl.models.pydantic.common import (
    ActivityDevice,
    PullDataNotification,
)


router = APIRouter(prefix="/transtek", tags=["transtek-router"])

settings = get_settings()
HEADER_NAME = "x-ciba-transtek-api-key"
TRANSTEK_CIBA_KEY = settings.TRANSTEK_CIBA_KEY


def get_mio_connect_client() -> MioConnectClient:
    settings = get_settings()
    return MioConnectClient(api_key=settings.TRANSTEK_API_KEY)


@router.post("/pair_device")
async def pair_device(
    request_data: PairDeviceRequest,
    client: MioConnectClient = Depends(get_mio_connect_client),
):
    """Pair a device to a member"""
    logger.info(
        f"Starting device pairing process for member: {request_data.member_id}"
    )

    try:
        member = await get_member_by_request(request_data)
        device = await get_device_info(request_data, client)
        if device["status"] != "complete":
            await activate_device(device["serialNumber"], client)
        await save_device_pairing(member, device)

        logger.info(
            f"Device pairing completed successfully: "
            f"member_id={member.id}, device_id={device['serialNumber']}"
        )

        return {
            "paired": True,
            "device_id": device["serialNumber"],
            "member_id": str(member.id),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during device pairing: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during device pairing",
        )


@router.post("/forwardtelemetry", status_code=status.HTTP_201_CREATED)
async def forward_telemetry(
    request: Request,
    message: (
        TranstekStatusMessage
        | TranstekTelemetryMessage
        | TranstekStatusTestMessage
        | TranstekTelemetryTestMessage
    ),
    response: Response,
):
    if (
        HEADER_NAME not in request.headers
        or request.headers[HEADER_NAME] != TRANSTEK_CIBA_KEY
    ):
        json_body = await request.json()
        body = dumps(
            json_body,
            indent=4,
            sort_keys=True,
        )
        logger.error(f"Unauthorized request to Transtek Endpoint: {body}")
        response.status_code = status.HTTP_401_UNAUTHORIZED
        return

    json_message = dumps(
        message, default=lambda x: x.__dict__, indent=4, sort_keys=True
    )

    if isinstance(
        message, (TranstekStatusTestMessage, TranstekTelemetryTestMessage)
    ):
        logger.info(f"Test Message from MioConnect: \n{json_message}")
    elif isinstance(message, TranstekStatusMessage):
        logger.info(f"Status from device: {json_message}")
    elif isinstance(message, TranstekTelemetryMessage):
        device = (
            await Transtek.filter(device_id=message.deviceId)
            .first()
            .prefetch_related("member", "member__platforms")
        )

        if device is None or device.member is None:
            logger.warning(
                f"Unpaired device with ID {message.deviceId} has sent telemetry: \n{json_message}"
            )
            return

        logger.info(f"Telemetry from device: {json_message}")

        try:
            queue = get_queue(settings.SQS_DATA_NOTIFICATION_QUEUE)

            member: Member = device.member

            correlation_id = request.headers.get(
                "X-Request-ID", str(pendulum.now().int_timestamp)
            )

            platforms = await member.get_platforms()

            notification = PullDataNotification(
                member_id=str(member.id),
                activity_device=ActivityDevice.TRANSTEK,
                platforms=platforms,
                payload=message,
                corellation_id=correlation_id,
            )
            queue_message = notification.model_dump_json()

            await queue.send(queue_message)
        except SendQueueException as exception:
            error_message = f"sending notification to the queue failed for queue {settings.SQS_DATA_NOTIFICATION_QUEUE}"
            logger.exception(error_message, exc_info=exception)
    else:
        logger.error("Unhandled transtek message type")

    return


@router.post(
    "/tracking-data",
    response_model=UpdateTrackingDataResponse,
    status_code=status.HTTP_200_OK,
)
async def update_tracking_data(
    request_data: UpdateTrackingDataRequest,
) -> UpdateTrackingDataResponse:
    """Update tracking data for a Transtek device."""
    try:
        tracking_url = await TranstekRepository.update_tracking_data(
            device_id=request_data.serial_number,
            imei=request_data.imei,
            tracking_number=request_data.tracking_number,
            carrier=request_data.carrier,
        )

        return UpdateTrackingDataResponse(tracking_url=tracking_url)

    except ValueError as e:
        logger.error(f"Validation error updating tracking data: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error updating tracking data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update tracking data",
        )


@router.get(
    "/carriers",
    response_model=CarrierListResponse,
    status_code=status.HTTP_200_OK,
)
async def get_carriers() -> CarrierListResponse:
    """
    Returns a list of supported carriers and their tracking link templates.
    """
    try:
        return CarrierListResponse(
            carriers=TRACKING_URLS,
        )
    except Exception as e:
        logger.error(f"Error retrieving carrier list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve carrier list",
        )


@router.get(
    "/device",
    response_model=TranstekResponse,
    status_code=status.HTTP_200_OK,
)
async def get_device(
    member_id: Optional[str] = Query(None, description="Filter by member ID"),
    device_id: Optional[str] = Query(None, description="Filter by device ID"),
    imei: Optional[str] = Query(None, description="Filter by IMEI"),
):
    """
    Get Transtek device by one of: member_id, device_id, or imei.
    Exactly one parameter must be provided.
    """
    params = [member_id, device_id, imei]
    provided_params = [p for p in params if p is not None]

    if len(provided_params) == 0:
        raise HTTPException(
            status_code=400,
            detail="At least one parameter is required: member_id, device_id, or imei",
        )

    if len(provided_params) > 1:
        raise HTTPException(
            status_code=400,
            detail="Only one parameter should be provided at a time",
        )

    try:
        device = None

        if member_id is not None:
            device = await TranstekRepository.get_device_by_member_id(
                member_id
            )
        elif device_id is not None:
            device = await TranstekRepository.get_device_by_device_id(
                device_id
            )
        elif imei is not None:
            device = await TranstekRepository.get_device_by_imei(imei)

        if device is None:
            raise HTTPException(status_code=404, detail="Device not found")

        return TranstekResponse.model_validate(device)

    except HTTPException:
        # Re-raise HTTPExceptions (like 404) without modification
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Internal server error: {str(e)}"
        )
