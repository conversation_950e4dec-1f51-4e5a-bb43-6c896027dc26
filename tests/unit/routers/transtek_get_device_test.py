from unittest.mock import AsyncMock, Mock, patch
from uuid import uuid4
from datetime import datetime

import pytest
from fastapi.exceptions import HTTPException
from fastapi import status
from fastapi.testclient import TestClient

from ciba_iot_etl.models.db.transtek import Transtek
from ciba_iot_etl.models.pydantic.transtek import TranstekDeviceType, TranstekStatus
from ciba_iot_etl.models.pydantic.common import Carrier

from app.main import app
from tests.unit.common import TEST_EMAIL

test_member_id = str(uuid4())
test_device_id = "test_device_123"
test_imei = "123456789012345"
test_exception = Exception("Test exception")


@pytest.fixture
def client():
    return TestClient(app)


@pytest.fixture
def mock_transtek_device():
    """Configure a mock Transtek device for testing purposes"""
    device = Mock(spec=Transtek)
    device.id = uuid4()
    device.device_id = test_device_id
    device.imei = test_imei
    device.model = "GBS-2104-G"
    device.device_type = TranstekDeviceType.SCALE
    device.tracking_number = "1Z999AA1234567890"
    device.carrier = Carrier.UPS
    device.timezone = "UTC"
    device.last_status_report = {"status": "active"}
    device.status = TranstekStatus.ACTIVE
    device.created_at = datetime.now()
    device.updated_at = datetime.now()
    device.member_id = test_member_id
    return device


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_member_id",
    new_callable=AsyncMock,
)
async def test_get_device_by_member_id_success(
    mock_get_device_by_member_id, client, mock_transtek_device
):
    """get_device should successfully return device when found by member_id"""
    mock_get_device_by_member_id.return_value = mock_transtek_device

    response = client.get(f"/transtek/device?member_id={test_member_id}")

    print(f"Response status: {response.status_code}")
    print(f"Response content: {response.content}")
    print(f"Response json: {response.json()}")

    assert response.status_code == status.HTTP_200_OK
    
    response_data = response.json()
    assert "id" in response_data
    assert "device_id" in response_data
    assert "imei" in response_data
    assert "model" in response_data
    assert "device_type" in response_data
    assert "status" in response_data
    assert "created_at" in response_data
    assert "updated_at" in response_data
    
    assert response_data["device_id"] == test_device_id
    assert response_data["imei"] == test_imei
    assert response_data["model"] == "GBS-2104-G"
    
    mock_get_device_by_member_id.assert_called_once_with(test_member_id)


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_device_id",
    new_callable=AsyncMock,
)
async def test_get_device_by_device_id_success(
    mock_get_device_by_device_id, client, mock_transtek_device
):
    """get_device should successfully return device when found by device_id"""
    mock_get_device_by_device_id.return_value = mock_transtek_device

    response = client.get(f"/transtek/device?device_id={test_device_id}")

    assert response.status_code == status.HTTP_200_OK
    
    response_data = response.json()
    assert response_data["device_id"] == test_device_id
    assert response_data["imei"] == test_imei
    
    mock_get_device_by_device_id.assert_called_once_with(test_device_id)


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_imei",
    new_callable=AsyncMock,
)
async def test_get_device_by_imei_success(
    mock_get_device_by_imei, client, mock_transtek_device
):
    """get_device should successfully return device when found by imei"""
    mock_get_device_by_imei.return_value = mock_transtek_device

    response = client.get(f"/transtek/device?imei={test_imei}")

    assert response.status_code == status.HTTP_200_OK
    
    response_data = response.json()
    assert response_data["device_id"] == test_device_id
    assert response_data["imei"] == test_imei
    
    mock_get_device_by_imei.assert_called_once_with(test_imei)


@pytest.mark.asyncio
async def test_get_device_no_parameters(client):
    """get_device should return 400 when no parameters are provided"""
    response = client.get("/transtek/device")

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    response_data = response.json()
    assert "detail" in response_data
    assert (
        response_data["detail"] 
        == "At least one parameter is required: member_id, device_id, or imei"
    )


@pytest.mark.asyncio
async def test_get_device_multiple_parameters_member_id_and_device_id(client):
    """get_device should return 400 when multiple parameters are provided"""
    response = client.get(
        f"/transtek/device?member_id={test_member_id}&device_id={test_device_id}"
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    response_data = response.json()
    assert "detail" in response_data
    assert (
        response_data["detail"] 
        == "Only one parameter should be provided at a time"
    )


@pytest.mark.asyncio
async def test_get_device_multiple_parameters_member_id_and_imei(client):
    """get_device should return 400 when multiple parameters are provided"""
    response = client.get(
        f"/transtek/device?member_id={test_member_id}&imei={test_imei}"
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    response_data = response.json()
    assert "detail" in response_data
    assert (
        response_data["detail"] 
        == "Only one parameter should be provided at a time"
    )


@pytest.mark.asyncio
async def test_get_device_multiple_parameters_device_id_and_imei(client):
    """get_device should return 400 when multiple parameters are provided"""
    response = client.get(
        f"/transtek/device?device_id={test_device_id}&imei={test_imei}"
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    response_data = response.json()
    assert "detail" in response_data
    assert (
        response_data["detail"] 
        == "Only one parameter should be provided at a time"
    )


@pytest.mark.asyncio
async def test_get_device_all_three_parameters(client):
    """get_device should return 400 when all three parameters are provided"""
    response = client.get(
        f"/transtek/device?member_id={test_member_id}&device_id={test_device_id}&imei={test_imei}"
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    response_data = response.json()
    assert "detail" in response_data
    assert (
        response_data["detail"] 
        == "Only one parameter should be provided at a time"
    )


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_member_id",
    new_callable=AsyncMock,
)
async def test_get_device_by_member_id_not_found(
    mock_get_device_by_member_id, client
):
    """get_device should return 404 when device is not found by member_id"""
    mock_get_device_by_member_id.return_value = None

    response = client.get(f"/transtek/device?member_id={test_member_id}")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    
    response_data = response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Device not found"
    
    mock_get_device_by_member_id.assert_called_once_with(test_member_id)


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_device_id",
    new_callable=AsyncMock,
)
async def test_get_device_by_device_id_not_found(
    mock_get_device_by_device_id, client
):
    """get_device should return 404 when device is not found by device_id"""
    mock_get_device_by_device_id.return_value = None

    response = client.get(f"/transtek/device?device_id={test_device_id}")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    
    response_data = response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Device not found"
    
    mock_get_device_by_device_id.assert_called_once_with(test_device_id)


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_imei",
    new_callable=AsyncMock,
)
async def test_get_device_by_imei_not_found(mock_get_device_by_imei, client):
    """get_device should return 404 when device is not found by imei"""
    mock_get_device_by_imei.return_value = None

    response = client.get(f"/transtek/device?imei={test_imei}")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    
    response_data = response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Device not found"
    
    mock_get_device_by_imei.assert_called_once_with(test_imei)
