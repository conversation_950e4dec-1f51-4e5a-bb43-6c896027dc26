from ciba_iot_etl.models.db.member import Member
import pytest
from fastapi.testclient import TestClient
from fastapi import status
from unittest.mock import AsyncMock, MagicMock, patch

from app.main import app
from app.routers.transtek import HEADER_NAME, TRANSTEK_CIBA_KEY
from app.services.queue import Queue
from tests.unit.routers.transtek_messages_test import (
    SCALE_STATUS,
    SCALE_TELEMETRY,
    BPM_STATUS,
    BPM_ANALYSIS,
    BPM_TELEMETRY,
    UNKNOWN_DEVICE,
    TEST_STATUS,
    TEST_TELEMETRY,
    BPM_ERROR,
    SCALE_ERROR,
)

HEADERS = {f"{HEADER_NAME}": TRANSTEK_CIBA_KEY}
PATH = "/transtek/forwardtelemetry"


@pytest.fixture
def client():
    return TestClient(app)


@pytest.mark.asyncio
async def test_scale_status(client):
    response = client.post(PATH, json=SCALE_STATUS, headers=HEADERS)

    assert response.status_code == status.HTTP_201_CREATED


@pytest.mark.asyncio
async def test_bpm_status(client):
    response = client.post(PATH, json=BPM_STATUS, headers=HEADERS)

    assert response.status_code == status.HTTP_201_CREATED


class AsyncChainMock:
    def __init__(self, return_value):
        self.return_value = return_value
        self._filter_kwargs = None
        self._prefetch_args = None

    def filter(self, **kwargs):
        self._filter_kwargs = kwargs
        return self

    def first(self):
        return self

    def prefetch_related(self, *args):
        self._prefetch_args = args
        return self

    def __await__(self):
        async def _return():
            return self.return_value

        return _return().__await__()


def send_telemetry_request(telemetry, client):
    mock_queue = AsyncMock(spec=Queue)

    mock_device = MagicMock()
    mock_member = MagicMock(spec=Member)
    mock_member.id = "123"

    mock_device.member = mock_member
    mock_device.member.platforms = []

    mock_chain = AsyncChainMock(mock_device)

    with patch("app.routers.transtek.Transtek", mock_chain):
        with patch("app.routers.transtek.get_queue") as mock_get_queue:
            mock_get_queue.return_value = mock_queue
            response = client.post(PATH, json=telemetry, headers=HEADERS)

            return response


@pytest.mark.asyncio
async def test_bpm_telemetry(client):
    response = send_telemetry_request(BPM_TELEMETRY, client)
    assert response.status_code == status.HTTP_201_CREATED


@pytest.mark.asyncio
async def test_scale_telemetry(client):
    response = send_telemetry_request(SCALE_TELEMETRY, client)
    assert response.status_code == status.HTTP_201_CREATED


@pytest.mark.asyncio
async def test_bpm_analysis(client):
    response = client.post(PATH, json=BPM_ANALYSIS, headers=HEADERS)

    assert response.status_code == status.HTTP_201_CREATED


@pytest.mark.asyncio
async def test_test_status(client):
    response = client.post(PATH, json=TEST_STATUS, headers=HEADERS)

    assert response.status_code == status.HTTP_201_CREATED


@pytest.mark.asyncio
async def test_test_telemetry(client):
    response = client.post(PATH, json=TEST_TELEMETRY, headers=HEADERS)

    assert response.status_code == status.HTTP_201_CREATED


@pytest.mark.asyncio
async def test_scale_error(client):
    response = client.post(PATH, json=SCALE_ERROR, headers=HEADERS)

    assert response.status_code == status.HTTP_201_CREATED


@pytest.mark.asyncio
async def test_bpm_error(client):
    response = client.post(PATH, json=BPM_ERROR, headers=HEADERS)

    assert response.status_code == status.HTTP_201_CREATED


@pytest.mark.asyncio
async def test_unknown_payload(client):
    response = client.post(PATH, json=UNKNOWN_DEVICE, headers=HEADERS)

    assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.asyncio
async def test_wrong_api_key(client):
    response = client.post(
        PATH,
        json=BPM_ANALYSIS,
        headers={f"{HEADER_NAME}": "asdf"},
    )

    assert response.status_code == status.HTTP_401_UNAUTHORIZED
