from typing import Optional

from ciba_iot_etl.models.pydantic.transtek import TranstekStatus
from ciba_iot_etl.models.db.transtek import Transtek


class TranstekRepository:
    @staticmethod
    async def update_tracking_data(
        device_id: Optional[str],
        imei: Optional[str],
        tracking_number: str,
        carrier: str,
    ) -> str:
        """Update carrier data for a Transtek device."""
        if not device_id and not imei:
            raise ValueError("Either device_id or imei must be provided")

        if device_id:
            device = await Transtek.filter(device_id=device_id).first()
        else:
            device = await Transtek.filter(imei=imei).first()

        if not device:
            raise ValueError("Device not found")

        device.tracking_number = tracking_number
        device.carrier = carrier
        device.status = TranstekStatus.SHIPPED
        await device.save()

        return device.tracking_url

    @staticmethod
    async def get_device_by_member_id(member_id: str) -> Transtek:
        """Get Transtek device by member id."""
        return await Transtek.filter(member_id=member_id).first()

    @staticmethod
    async def get_device_by_device_id(device_id: str) -> Transtek:
        """Get Transtek device by device id."""
        return await Transtek.filter(device_id=device_id).first()

    @staticmethod
    async def get_device_by_imei(imei: str) -> Transtek:
        """Get Transtek device by imei."""
        return await Transtek.filter(imei=imei).first()
